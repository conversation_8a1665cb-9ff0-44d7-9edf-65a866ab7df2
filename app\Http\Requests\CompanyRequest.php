<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'legal_name' => ['nullable', 'string', 'max:255'],
            'ein' => ['required', 'string', 'max:20', 'regex:/^\d{2}-\d{7}$/'],
            'address_line_1' => ['required', 'string', 'max:255'],
            'address_line_2' => ['nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:100'],
            'state' => ['required', 'string', 'size:2'],
            'zip_code' => ['required', 'string', 'max:10'],
            'country' => ['required', 'string', 'size:2'],
            'phone' => ['nullable', 'string', 'max:20'],
            'email' => ['nullable', 'email', 'max:255'],
            'website' => ['nullable', 'url', 'max:255'],
            'entity_type' => ['required', 'in:corporation,llc,partnership,sole_proprietorship'],
            'incorporation_date' => ['nullable', 'date'],
            'fiscal_year_end' => ['required', 'in:01-31,02-28,03-31,04-30,05-31,06-30,07-31,08-31,09-30,10-31,11-30,12-31'],
        ];

        // For updates, make EIN unique except for current company
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $rules['ein'][] = 'unique:companies,ein,' . $this->route('company');
        } else {
            $rules['ein'][] = 'unique:companies,ein';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ein.regex' => 'The EIN must be in the format XX-XXXXXXX (e.g., 12-3456789).',
            'state.size' => 'The state must be a 2-letter code (e.g., CA, NY).',
            'country.size' => 'The country must be a 2-letter code (e.g., US, CA).',
        ];
    }
}
