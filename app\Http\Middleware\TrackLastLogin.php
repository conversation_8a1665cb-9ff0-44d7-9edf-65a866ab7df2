<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TrackLastLogin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            $user = auth()->user();

            // Only update if last login was more than 5 minutes ago to avoid too many updates
            if (!$user->last_login_at || $user->last_login_at->diffInMinutes(now()) > 5) {
                $user->updateLastLogin();
            }
        }

        return $next($request);
    }
}
