<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'legal_name',
        'ein',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'zip_code',
        'country',
        'phone',
        'email',
        'website',
        'entity_type',
        'incorporation_date',
        'fiscal_year_end',
        'is_active',
    ];

    protected $casts = [
        'incorporation_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the users that belong to the company.
     */
    public function users()
    {
        return $this->belongsToMany(User::class)
                    ->withPivot('role', 'is_active')
                    ->withTimestamps();
    }

    /**
     * Get the company's full address.
     */
    public function getFullAddressAttribute()
    {
        $address = $this->address_line_1;
        if ($this->address_line_2) {
            $address .= ', ' . $this->address_line_2;
        }
        $address .= ', ' . $this->city . ', ' . $this->state . ' ' . $this->zip_code;

        return $address;
    }

    /**
     * Get the display name (legal name if available, otherwise name).
     */
    public function getDisplayNameAttribute()
    {
        return $this->legal_name ?: $this->name;
    }

    /**
     * Scope to get only active companies.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
