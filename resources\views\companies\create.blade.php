<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Company') }}
            </h2>
            <a href="{{ route('companies.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                {{ __('Back to Companies') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('companies.store') }}" class="space-y-6">
                        @csrf

                        <!-- Company Information -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Company Information') }}</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <x-input-label for="name" :value="__('Company Name')" />
                                    <x-text-input id="name" name="name" type="text" class="mt-1 block w-full" :value="old('name')" required autofocus />
                                    <x-input-error class="mt-2" :messages="$errors->get('name')" />
                                </div>

                                <div>
                                    <x-input-label for="legal_name" :value="__('Legal Name (if different)')" />
                                    <x-text-input id="legal_name" name="legal_name" type="text" class="mt-1 block w-full" :value="old('legal_name')" />
                                    <x-input-error class="mt-2" :messages="$errors->get('legal_name')" />
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div>
                                    <x-input-label for="ein" :value="__('EIN (XX-XXXXXXX)')" />
                                    <x-text-input id="ein" name="ein" type="text" class="mt-1 block w-full" :value="old('ein')" required placeholder="12-3456789" />
                                    <x-input-error class="mt-2" :messages="$errors->get('ein')" />
                                </div>

                                <div>
                                    <x-input-label for="entity_type" :value="__('Entity Type')" />
                                    <select id="entity_type" name="entity_type" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm" required>
                                        <option value="">{{ __('Select Entity Type') }}</option>
                                        <option value="corporation" {{ old('entity_type') == 'corporation' ? 'selected' : '' }}>{{ __('Corporation') }}</option>
                                        <option value="llc" {{ old('entity_type') == 'llc' ? 'selected' : '' }}>{{ __('LLC') }}</option>
                                        <option value="partnership" {{ old('entity_type') == 'partnership' ? 'selected' : '' }}>{{ __('Partnership') }}</option>
                                        <option value="sole_proprietorship" {{ old('entity_type') == 'sole_proprietorship' ? 'selected' : '' }}>{{ __('Sole Proprietorship') }}</option>
                                    </select>
                                    <x-input-error class="mt-2" :messages="$errors->get('entity_type')" />
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div>
                                    <x-input-label for="incorporation_date" :value="__('Incorporation Date')" />
                                    <x-text-input id="incorporation_date" name="incorporation_date" type="date" class="mt-1 block w-full" :value="old('incorporation_date')" />
                                    <x-input-error class="mt-2" :messages="$errors->get('incorporation_date')" />
                                </div>

                                <div>
                                    <x-input-label for="fiscal_year_end" :value="__('Fiscal Year End')" />
                                    <select id="fiscal_year_end" name="fiscal_year_end" class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm" required>
                                        <option value="12-31" {{ old('fiscal_year_end', '12-31') == '12-31' ? 'selected' : '' }}>{{ __('December 31') }}</option>
                                        <option value="01-31" {{ old('fiscal_year_end') == '01-31' ? 'selected' : '' }}>{{ __('January 31') }}</option>
                                        <option value="02-28" {{ old('fiscal_year_end') == '02-28' ? 'selected' : '' }}>{{ __('February 28') }}</option>
                                        <option value="03-31" {{ old('fiscal_year_end') == '03-31' ? 'selected' : '' }}>{{ __('March 31') }}</option>
                                        <option value="04-30" {{ old('fiscal_year_end') == '04-30' ? 'selected' : '' }}>{{ __('April 30') }}</option>
                                        <option value="05-31" {{ old('fiscal_year_end') == '05-31' ? 'selected' : '' }}>{{ __('May 31') }}</option>
                                        <option value="06-30" {{ old('fiscal_year_end') == '06-30' ? 'selected' : '' }}>{{ __('June 30') }}</option>
                                        <option value="07-31" {{ old('fiscal_year_end') == '07-31' ? 'selected' : '' }}>{{ __('July 31') }}</option>
                                        <option value="08-31" {{ old('fiscal_year_end') == '08-31' ? 'selected' : '' }}>{{ __('August 31') }}</option>
                                        <option value="09-30" {{ old('fiscal_year_end') == '09-30' ? 'selected' : '' }}>{{ __('September 30') }}</option>
                                        <option value="10-31" {{ old('fiscal_year_end') == '10-31' ? 'selected' : '' }}>{{ __('October 31') }}</option>
                                        <option value="11-30" {{ old('fiscal_year_end') == '11-30' ? 'selected' : '' }}>{{ __('November 30') }}</option>
                                    </select>
                                    <x-input-error class="mt-2" :messages="$errors->get('fiscal_year_end')" />
                                </div>
                            </div>
                        </div>

                        <!-- Address Information -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Address Information') }}</h3>
                            
                            <div>
                                <x-input-label for="address_line_1" :value="__('Address Line 1')" />
                                <x-text-input id="address_line_1" name="address_line_1" type="text" class="mt-1 block w-full" :value="old('address_line_1')" required />
                                <x-input-error class="mt-2" :messages="$errors->get('address_line_1')" />
                            </div>

                            <div class="mt-4">
                                <x-input-label for="address_line_2" :value="__('Address Line 2 (Optional)')" />
                                <x-text-input id="address_line_2" name="address_line_2" type="text" class="mt-1 block w-full" :value="old('address_line_2')" />
                                <x-input-error class="mt-2" :messages="$errors->get('address_line_2')" />
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                                <div class="md:col-span-2">
                                    <x-input-label for="city" :value="__('City')" />
                                    <x-text-input id="city" name="city" type="text" class="mt-1 block w-full" :value="old('city')" required />
                                    <x-input-error class="mt-2" :messages="$errors->get('city')" />
                                </div>

                                <div>
                                    <x-input-label for="state" :value="__('State')" />
                                    <x-text-input id="state" name="state" type="text" class="mt-1 block w-full" :value="old('state')" required maxlength="2" placeholder="CA" />
                                    <x-input-error class="mt-2" :messages="$errors->get('state')" />
                                </div>

                                <div>
                                    <x-input-label for="zip_code" :value="__('ZIP Code')" />
                                    <x-text-input id="zip_code" name="zip_code" type="text" class="mt-1 block w-full" :value="old('zip_code')" required />
                                    <x-input-error class="mt-2" :messages="$errors->get('zip_code')" />
                                </div>
                            </div>

                            <div class="mt-4">
                                <x-input-label for="country" :value="__('Country')" />
                                <x-text-input id="country" name="country" type="text" class="mt-1 block w-full" :value="old('country', 'US')" required maxlength="2" />
                                <x-input-error class="mt-2" :messages="$errors->get('country')" />
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Contact Information') }}</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <x-input-label for="phone" :value="__('Phone')" />
                                    <x-text-input id="phone" name="phone" type="tel" class="mt-1 block w-full" :value="old('phone')" />
                                    <x-input-error class="mt-2" :messages="$errors->get('phone')" />
                                </div>

                                <div>
                                    <x-input-label for="email" :value="__('Email')" />
                                    <x-text-input id="email" name="email" type="email" class="mt-1 block w-full" :value="old('email')" />
                                    <x-input-error class="mt-2" :messages="$errors->get('email')" />
                                </div>
                            </div>

                            <div class="mt-4">
                                <x-input-label for="website" :value="__('Website')" />
                                <x-text-input id="website" name="website" type="url" class="mt-1 block w-full" :value="old('website')" placeholder="https://example.com" />
                                <x-input-error class="mt-2" :messages="$errors->get('website')" />
                            </div>
                        </div>

                        <div class="flex items-center justify-end space-x-4">
                            <a href="{{ route('companies.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                {{ __('Cancel') }}
                            </a>
                            <x-primary-button>
                                {{ __('Create Company') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
