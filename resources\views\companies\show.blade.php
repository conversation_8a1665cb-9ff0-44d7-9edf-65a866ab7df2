<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $company->display_name }}
            </h2>
            <div class="flex space-x-2">
                @can('update', $company)
                    <a href="{{ route('companies.edit', $company) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        {{ __('Edit Company') }}
                    </a>
                @endcan
                <a href="{{ route('companies.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    {{ __('Back to Companies') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Company Overview -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Basic Information -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Company Information') }}</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ __('Company Name') }}</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->name }}</dd>
                                </div>
                                @if($company->legal_name)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Legal Name') }}</dt>
                                        <dd class="text-sm text-gray-900">{{ $company->legal_name }}</dd>
                                    </div>
                                @endif
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ __('EIN') }}</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->ein }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ __('Entity Type') }}</dt>
                                    <dd class="text-sm text-gray-900">{{ ucfirst(str_replace('_', ' ', $company->entity_type)) }}</dd>
                                </div>
                                @if($company->incorporation_date)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Incorporation Date') }}</dt>
                                        <dd class="text-sm text-gray-900">{{ $company->incorporation_date->format('M d, Y') }}</dd>
                                    </div>
                                @endif
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">{{ __('Fiscal Year End') }}</dt>
                                    <dd class="text-sm text-gray-900">{{ $company->fiscal_year_end }}</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Address Information -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Address') }}</h3>
                            <address class="text-sm text-gray-900 not-italic">
                                {{ $company->address_line_1 }}<br>
                                @if($company->address_line_2)
                                    {{ $company->address_line_2 }}<br>
                                @endif
                                {{ $company->city }}, {{ $company->state }} {{ $company->zip_code }}<br>
                                {{ $company->country }}
                            </address>
                        </div>

                        <!-- Contact Information -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Contact Information') }}</h3>
                            <dl class="space-y-2">
                                @if($company->phone)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Phone') }}</dt>
                                        <dd class="text-sm text-gray-900">{{ $company->phone }}</dd>
                                    </div>
                                @endif
                                @if($company->email)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Email') }}</dt>
                                        <dd class="text-sm text-gray-900">
                                            <a href="mailto:{{ $company->email }}" class="text-blue-600 hover:text-blue-800">
                                                {{ $company->email }}
                                            </a>
                                        </dd>
                                    </div>
                                @endif
                                @if($company->website)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">{{ __('Website') }}</dt>
                                        <dd class="text-sm text-gray-900">
                                            <a href="{{ $company->website }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                                {{ $company->website }}
                                            </a>
                                        </dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Quick Actions') }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <a href="#" class="bg-blue-50 hover:bg-blue-100 p-4 rounded-lg border border-blue-200 transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ __('Chart of Accounts') }}</p>
                                    <p class="text-sm text-gray-500">{{ __('Manage accounts') }}</p>
                                </div>
                            </div>
                        </a>

                        <a href="#" class="bg-green-50 hover:bg-green-100 p-4 rounded-lg border border-green-200 transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ __('New Transaction') }}</p>
                                    <p class="text-sm text-gray-500">{{ __('Record entry') }}</p>
                                </div>
                            </div>
                        </a>

                        <a href="#" class="bg-purple-50 hover:bg-purple-100 p-4 rounded-lg border border-purple-200 transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ __('Reports') }}</p>
                                    <p class="text-sm text-gray-500">{{ __('Financial reports') }}</p>
                                </div>
                            </div>
                        </a>

                        <a href="#" class="bg-orange-50 hover:bg-orange-100 p-4 rounded-lg border border-orange-200 transition-colors duration-200">
                            <div class="flex items-center">
                                <svg class="h-8 w-8 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ __('Form 1120') }}</p>
                                    <p class="text-sm text-gray-500">{{ __('Tax preparation') }}</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Recent Activity') }}</h3>
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">{{ __('No recent activity') }}</h3>
                        <p class="mt-1 text-sm text-gray-500">{{ __('Activity will appear here once you start using the system.') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
