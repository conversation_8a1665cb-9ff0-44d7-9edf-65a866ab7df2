<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Accounting System - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">Laravel Accounting System</h1>
                    <p class="text-gray-600 mb-6">A comprehensive accounting system built with Laravel best practices.</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h2 class="text-xl font-semibold text-blue-900 mb-3">✅ Completed Features</h2>
                            <ul class="space-y-2 text-blue-800">
                                <li>• User Authentication & Registration</li>
                                <li>• Enhanced User Profiles</li>
                                <li>• Company Management (CRUD)</li>
                                <li>• Role-based Access Control</li>
                                <li>• Responsive Dashboard</li>
                                <li>• Database Migrations</li>
                                <li>• Form Validation</li>
                                <li>• Authorization Policies</li>
                            </ul>
                        </div>
                        
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h2 class="text-xl font-semibold text-green-900 mb-3">🚀 Next Phase</h2>
                            <ul class="space-y-2 text-green-800">
                                <li>• Chart of Accounts</li>
                                <li>• Double-Entry Transactions</li>
                                <li>• Journal Entries</li>
                                <li>• General Ledger</li>
                                <li>• Balance Sheet</li>
                                <li>• Income Statement</li>
                                <li>• IRS Form 1120 Export</li>
                                <li>• PDF/Excel Reports</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-8 p-4 bg-yellow-50 rounded-lg">
                        <h3 class="text-lg font-semibold text-yellow-900 mb-2">Database Setup Required</h3>
                        <p class="text-yellow-800 mb-4">To run the full application, you need to set up a database:</p>
                        <div class="space-y-2 text-sm text-yellow-800">
                            <p><strong>Option 1 - MySQL:</strong> Install XAMPP/WAMP, create database "accounting_system"</p>
                            <p><strong>Option 2 - SQLite:</strong> Enable SQLite PHP extension</p>
                            <p><strong>Then run:</strong> <code class="bg-yellow-200 px-2 py-1 rounded">php artisan migrate && php artisan db:seed</code></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>