[2025-06-01 08:58:43] local.ERROR: could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#13 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#14 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(159): retry(1, Object(Closure), 0, Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('sqlite:C:\\\\Users...', NULL, NULL, Array)
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:C:\\\\Users...', NULL, NULL, Array)
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(37): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:C:\\\\Users...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(159): retry(1, Object(Closure), 0, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#33 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 {main}
"} 
