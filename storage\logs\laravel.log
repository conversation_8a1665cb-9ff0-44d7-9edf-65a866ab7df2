[2025-06-01 08:58:43] local.ERROR: could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#13 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#14 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(159): retry(1, Object(Closure), 0, Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('sqlite:C:\\\\Users...', NULL, NULL, Array)
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:C:\\\\Users...', NULL, NULL, Array)
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(37): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:C:\\\\Users...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(159): retry(1, Object(Closure), 0, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#33 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 {main}
"} 
[2025-06-01 09:08:55] local.ERROR: could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#13 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#14 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(159): retry(1, Object(Closure), 0, Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('sqlite:C:\\\\Users...', NULL, NULL, Array)
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:C:\\\\Users...', NULL, NULL, Array)
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(37): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:C:\\\\Users...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(159): retry(1, Object(Closure), 0, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#33 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 {main}
"} 
[2025-06-01 09:10:03] local.ERROR: could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#13 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#14 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(159): retry(1, Object(Closure), 0, Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('sqlite:C:\\\\Users...', NULL, NULL, Array)
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:C:\\\\Users...', NULL, NULL, Array)
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(37): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:C:\\\\Users...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(226): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(182): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->getConnection()
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(159): retry(1, Object(Closure), 0, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#33 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 {main}
"} 
[2025-06-01 09:10:05] local.ERROR: could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1820): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1786): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1577): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1496): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1532): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1485): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\database\\seeders\\AdminUserSeeder.php(25): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\AdminUserSeeder->run()
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\database\\seeders\\DatabaseSeeder.php(16): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#33 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#35 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#36 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#38 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#39 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#42 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#43 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#45 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#49 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#50 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('sqlite:C:\\\\Users...', NULL, NULL, Array)
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:C:\\\\Users...', NULL, NULL, Array)
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(37): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:C:\\\\Users...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1820): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1786): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1577): Illuminate\\Database\\Eloquent\\Model->getConnection()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1496): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1532): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1485): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->newQuery()
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\database\\seeders\\AdminUserSeeder.php(25): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\AdminUserSeeder->run()
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#32 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#34 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\database\\seeders\\DatabaseSeeder.php(16): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#35 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#36 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#41 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#42 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#43 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#44 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#46 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#47 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#50 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#51 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#52 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#53 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#54 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#56 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#57 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#58 {main}
"} 
[2025-06-01 09:11:08] local.ERROR: could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) at C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1820): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1786): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1577): Illuminate\\Database\\Eloquent\\Model->getConnection()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1496): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#13 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1532): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#14 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1485): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(167): Illuminate\\Database\\Eloquent\\Model->newQuery()
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(127): Illuminate\\Auth\\EloquentUserProvider->newModelQuery()
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(381): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(340): Illuminate\\Auth\\SessionGuard->attempt(Array, false)
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\app\\Http\\Requests\\Auth\\LoginRequest.php(44): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php(28): App\\Http\\Requests\\Auth\\LoginRequest->authenticate()
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\AuthenticatedSessionController->store(Object(App\\Http\\Requests\\Auth\\LoginRequest))
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\AuthenticatedSessionController), 'store')
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('sqlite:C:\\\\Users...', NULL, NULL, Array)
#1 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:C:\\\\Users...', NULL, NULL, Array)
#2 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(37): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:C:\\\\Users...', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#8 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(588): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(561): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#11 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#12 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(76): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\Users\\\\<USER>\\Users\\alejandrolopez\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#15 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#16 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#17 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1820): Illuminate\\Database\\DatabaseManager->connection('sqlite')
#18 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1786): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#19 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1577): Illuminate\\Database\\Eloquent\\Model->getConnection()
#20 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1496): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#21 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1532): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#22 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1485): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#23 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(167): Illuminate\\Database\\Eloquent\\Model->newQuery()
#24 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(127): Illuminate\\Auth\\EloquentUserProvider->newModelQuery()
#25 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(381): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#26 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(340): Illuminate\\Auth\\SessionGuard->attempt(Array, false)
#27 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#28 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\app\\Http\\Requests\\Auth\\LoginRequest.php(44): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#29 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php(28): App\\Http\\Requests\\Auth\\LoginRequest->authenticate()
#30 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\AuthenticatedSessionController->store(Object(App\\Http\\Requests\\Auth\\LoginRequest))
#31 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#32 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\AuthenticatedSessionController), 'store')
#33 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#34 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#35 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#55 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Documents\\augment-projects\\accountingsoftware\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>